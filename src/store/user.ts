// import { defineStore } from 'pinia'
// import { ref } from 'vue'

// const initState = { nickname: '', avatar: '' }

// export const useUserStore = defineStore(
//   'user',
//   () => {
//     const userInfo = ref<IUserInfo>({ ...initState })

//     const setUserInfo = (val: IUserInfo) => {
//       userInfo.value = val
//     }

//     const clearUserInfo = () => {
//       userInfo.value = { ...initState }
//     }
//     // 一般没有reset需求，不需要的可以删除
//     const reset = () => {
//       userInfo.value = { ...initState }
//     }
//     const isLogined = computed(() => !!userInfo.value.token)

//     return {
//       userInfo,
//       setUserInfo,
//       clearUserInfo,
//       isLogined,
//       reset,
//     }
//   },
//   {
//     persist: true,
//   },
// )
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { http } from '@/utils/http'
// import { $api } from '@/api/index'

const initState = {
  userName: '',
  id: '',
  photoUrl: '',
  token: '',
  syscode: '',
}

interface IUserQuery {
  sysCode: string
  token: string
}

export const useUserStore = defineStore(
  'inspect-user',
  () => {
    // 临时ticket,用于获取token
    const userTicket = ref('')
    const setUserTicket = (val: string) => {
      userTicket.value = val
    }
    const clearUserTicket = () => {
      userTicket.value = ''
    }

    const userInfo = ref<IUserInfo>({ ...initState })
    const setUserInfo = (val: IUserInfo) => {
      userInfo.value = val
    }
    const clearUserInfo = () => {
      userInfo.value = { ...initState }
    }

    const isLogined = computed(() => !!userInfo.value.token)

    // 获取用户信息
    const getUserInfo = async (query: IUserQuery) => {
      const res: any = await http.get(
        import.meta.env.VITE_SERVER_PORT + '/login/getTokenLoginInfoByToken',
        query,
      )
      return res
      // const url = `/login/getTokenLoginInfoByToken?sysCode=${query.sysCode}&token=${query.token}`
      // return http.get(url)
      // return http<IUserInfo>({
      //   url,
      //   query,
      //   method: 'GET',
      //   noToken: true,
      // })
    }

    // checkSysPower接口获取token
    // const checkSysPower = async (parameters: any) => {
    //   return http.post($api.type.platform + '/login/checkSysPower', parameters)
    // }

    return {
      userInfo,
      setUserInfo,
      clearUserInfo,
      userTicket,
      setUserTicket,
      clearUserTicket,
      isLogined,
      getUserInfo,
      // checkSysPower,
    }
  },
  {
    persist: true,
  },
)
