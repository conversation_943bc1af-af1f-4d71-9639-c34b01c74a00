import { useUserStore, useAppStore, useAppReceiveStore } from '@/store'
import { currRoute, getUrlObj } from '@/utils/index'
import { parseQueryString } from '@/hooks/getParams'
import { http } from '@/utils/http'

export function wvSubscribe() {
  console.log('wvSubscribe>>>>init')

  const u = decodeURIComponent(window.location.href)
  const paramData: any = parseQueryString(u)
  if (paramData.location) {
    uni.setStorageSync('locationCustom', JSON.parse(paramData.location))
  }
  uni.setStorageSync('FromParentParam', paramData)
  const appStore = useAppStore()
  const receiveStore = useAppReceiveStore()

  // 监听UniAppJSBridgeReady
  document.addEventListener('UniAppJSBridgeReady', function () {
    console.log('onLaunch>>>>')

    // 设置当前环境
    // uni.webView.getEnv(function (res) {
    //   console.log('onLaunch>当前环境：' + JSON.stringify(res))
    //   // appStore.setAppEnv(res)
    // })
  })

  // 全局监听外部应用消息
  window.$receiveData = function (data) {
    receiveStore.setAppReceiveData(data)
  }
}

export async function beforeEnter() {
  const userStore = useUserStore()
  const appStore = useAppStore()

  // 解决下面path初始获取不到的问题
  const _herf = window.location.href
  const u = decodeURIComponent(window.location.href)
  const paramData: any = parseQueryString(u)
  console.log('herf>>>>', paramData)
  const { redirect, safetyArea, ticket, syscode, appEnv, osName, flag } = paramData // 设置当前环境

  appStore.setAppEnv({
    plus: appEnv === 'app',
    miniprogram: appEnv === 'mp-weixin',
    osAndroid: osName === 'android',
    osIos: osName === 'ios',
    changhangh5: appEnv !== 'app' && appEnv !== 'mp-weixin',
  })
  // 设置用户ticket
  if (paramData.ticket) {
    // 3. 根据userTicket获取token
    try {
      userStore.setUserTicket(ticket)
      // 3. 根据userTicket获取token
      try {
        const _res = await userStore.getUserInfo({ sysCode: syscode, token: ticket })
        if (+_res.code === 200) {
          userStore.setUserInfo(_res.data)
        } else {
          uni.navigateTo({ url: '/pages/error/error' })
        }
      } catch (error) {
        // console.log('getToken>>>err', error)
        uni.navigateTo({ url: '/pages/error/error' })
      }
    } catch (err) {
      uni.navigateTo({ url: '/pages/error/error' })
    }
  } else {
    uni.navigateTo({ url: '/pages/error/error' })
  }
}
