<template>
  <view class="taskList">
    <view class="head">
      <view class="point">
        <view>{{ data.num }}</view>
        <view>检查点</view>
      </view>
      <view>{{ data.title }}</view>
      <view class="status" :style="{ 'background-color': getStatusColor(data.taskStatus) }">
        {{ data.taskStatusName }}
      </view>
    </view>
    <view class="error" v-if="data.taskStatus == 1">
      异常事件：
      <text>{{ data.yichang }}</text>
    </view>
    <view class="img" style="padding: 12px">
      <image
        class="no-start-image"
        src="@/pages/index/assets/item-no-start.png"
        style="width: 321px; height: 180px; border-radius: 6px"
        v-if="data.imgSrc == ''"
      />
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { getFullFileUrl, getFullThumbnailUrl } from '@/utils/fileUrl'
import Empty from '@/components/empty.vue'

defineProps({
  data: {
    type: Array as any,
    default: () => {
      return []
    },
  },
})

// 定义状态颜色配置
const STATUS_CONFIG = {
  0: { label: '待开始', color: '#ffc107' }, // 黄色
  1: { label: '异常', color: '#fa1d27' }, // 黄色
  2: { label: '进行中', color: '#2196f3' }, // 蓝色
  3: { label: '已完成', color: '#4caf50' }, // 绿色
} as const
const getStatusColor = (state: number): string => {
  return STATUS_CONFIG[state as keyof typeof STATUS_CONFIG]?.color || '#9e9e9e'
}

// 查看详情
const toDetail = (item: any) => {
  uni.navigateTo({
    url: `/pages/inspectTaskDetail/inspectTaskDetail?id=${item.id}`,
  })
}
</script>

<style lang="scss" scoped>
.taskList {
  width: 345px;

  margin-bottom: 10px;
  background: white;
  border-radius: 6px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  .head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    .point {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      color: white;
      text-align: center;
      background-color: #fbae49;
      border-radius: 5px;
    }
    .status {
      width: 44px;
      height: 19px;
      line-height: 19px;
      color: white;
      text-align: center;
      border-radius: 2px;
    }
  }
  .error {
    padding: 12px;
  }
}
</style>
