<template>
  <view class="taskDetail">
    <view class="info">
      <view>
        <text>所属单位：</text>
        {{ info.deptName || '--' }}
      </view>
      <view>
        <text>任务时间：</text>
        {{ info.taskPlanStartTime }} ~ {{ info.taskPlanEndTime }}
      </view>
      <view>
        <text>任务状态：</text>
        {{ info.taskStatusName }}
      </view>
      <view>
        <text>视频巡检点位：</text>
        {{ info.allNum }}个
      </view>
    </view>
    <view class="card">
      <view
        class="li"
        v-for="(item, index) in cards"
        :key="index"
        :class="index == zIndex ? 'Active' : ''"
        @click="changeActive(index)"
      >
        <view class="label">{{ item.label }}</view>
        <view class="value">{{ item.value }}{{ item.suffix || '' }}</view>
      </view>
    </view>
    <view class="table">
      <Table v-for="item in tableData" :key="item.id" :data="item" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

import { taskInspectionDetailStatisticAPI } from '../fetch.ts'
import Table from './accidentList.vue'

const props = defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
  tableData: {
    type: Array,
    default: () => [],
  },
})

const zIndex = ref(0)

const getStatic = (id) => {
  taskInspectionDetailStatisticAPI({ taskId: id }).then((res) => {
    const data = res.data || {}
    // 按key映射值
    cards.value = cards.value.map((c) => {
      const value = Number(data[c.key] ?? 0)
      return { ...c, value }
    })
  })
}

const cards = ref<any[]>([
  { label: '已巡查', value: 0, key: 'finishedNum' },
  { label: '待巡查', value: 0, key: 'waitOpenNum' },
  { label: '异常事件', value: 0, key: 'hazardNum' },
  { label: '巡检进度', value: 0, key: 'progress', suffix: '%' },
])

const changeActive = (index: number) => {
  zIndex.value = index
}

const tId = ref('')
onLoad((o) => {
  tId.value = o.id
  getStatic(o.id)
})
</script>

<style scoped lang="scss">
.Active {
  background: linear-gradient(180deg, #568eff 0%, #2167f1 100%);
  .label {
    font-size: 12px !important;
    color: #ffffff !important;
  }
  .value {
    font-size: 22px !important;
    font-weight: 500;
    color: #ffffff !important;
  }
}

.taskDetail {
  font-size: 14px;
  .info {
    padding: 10px;
    background-color: white;
    view {
      margin-bottom: 12px;
      text {
        color: #7f7f7f;
      }
    }
  }
  .card {
    display: flex;
    justify-content: space-between;
    padding: 8px;
    margin-top: 26px;
    margin-bottom: 26px;
    background-color: white;
    .li {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 78px;
      height: 58px;
      text-align: center;
      // background: linear-gradient(180deg, #568eff 0%, #2167f1 100%);
      background-color: #ebf1ffff;
      border-radius: 8px 8px 8px 8px;
      .label {
        font-size: 12px;
        color: #0256ffff;
      }
      .value {
        font-size: 22px;
        font-weight: 500;
        color: #0256ffff;
      }
    }
  }
  .table {
    width: 345px;

    // padding: 5v;
    margin: 0 auto;
    margin-bottom: 50px;
  }
}
</style>
