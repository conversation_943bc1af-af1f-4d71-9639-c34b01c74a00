<template>
  <view class="manifest">
    <!-- 筛选条件 -->
    <view class="filter-bar">
      <view class="filter-item">
        <text>全部类型</text>
        <text class="arrow">▼</text>
      </view>
      <view class="filter-item">
        <text>处置状态</text>
        <text class="arrow">▼</text>
      </view>
      <view class="filter-item">
        <text>2025-01-01 - 2025-09-01</text>
        <text class="arrow">▼</text>
      </view>
    </view>

    <!-- 事件列表 -->
    <view class="event-list">
      <view
        class="event-item"
        v-for="(item, index) in tableData"
        :key="index"
        @click="handleItemClick(item)"
      >
        <view class="event-content">
          <view class="event-header">
            <view class="event-tag" :class="getTagClass(item.eventType)">
              {{ item.eventTypeName || '人员离岗' }}
            </view>
          </view>
          <view class="event-desc">
            {{ item.description || '外运垃圾车辆空调器清洗一层中控室' }}
          </view>
          <view class="event-time">
            {{ item.createTime || '2025-05-06 14:56:12' }}
          </view>
        </view>
        <view class="event-right">
          <image
            class="event-image"
            :src="item.imageUrl || '/static/images/default-event.png'"
            mode="aspectFill"
          />
          <text class="arrow-right">›</text>
        </view>
      </view>
    </view>

    <!-- 底部提示 -->
    <view class="bottom-tip" v-if="tableData.length === 0">暂无相关事件</view>
  </view>
</template>

<script setup lang="ts">
defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
  tableData: {
    type: Array,
    default: () => [],
  },
})

// 获取事件类型标签样式
const getTagClass = (eventType: string) => {
  const typeMap: Record<string, string> = {
    人员离岗: 'tag-orange',
    疑似烟火: 'tag-green',
    异常行为: 'tag-red',
    设备异常: 'tag-blue',
  }
  return typeMap[eventType] || 'tag-orange'
}

// 处理事件项点击
const handleItemClick = (item: any) => {
  console.log('点击事件项:', item)
  // 这里可以添加跳转到事件详情页的逻辑
  uni.navigateTo({
    url: `/pages/eventDetail/eventDetail?id=${item.id}`,
  })
}
</script>

<style scoped lang="scss">
.manifest {
  background-color: #f7f7f7;
  min-height: 100vh;

  .filter-bar {
    display: flex;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: white;
    border-bottom: 1px solid #f0f0f0;

    .filter-item {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #666;

      .arrow {
        margin-left: 4px;
        font-size: 12px;
      }
    }
  }

  .event-list {
    padding: 0 16px;

    .event-item {
      display: flex;
      align-items: center;
      padding: 16px 0;
      background-color: white;
      border-radius: 8px;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .event-content {
        flex: 1;
        padding: 0 16px;

        .event-header {
          margin-bottom: 8px;

          .event-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            color: white;

            &.tag-orange {
              background-color: #ff9500;
            }

            &.tag-green {
              background-color: #34c759;
            }

            &.tag-red {
              background-color: #ff3b30;
            }

            &.tag-blue {
              background-color: #007aff;
            }
          }
        }

        .event-desc {
          font-size: 14px;
          color: #333;
          line-height: 1.4;
          margin-bottom: 8px;
        }

        .event-time {
          font-size: 12px;
          color: #999;
        }
      }

      .event-right {
        display: flex;
        align-items: center;
        padding-right: 16px;

        .event-image {
          width: 60px;
          height: 40px;
          border-radius: 4px;
          margin-right: 8px;
        }

        .arrow-right {
          font-size: 18px;
          color: #ccc;
        }
      }
    }
  }

  .bottom-tip {
    text-align: center;
    padding: 40px 0;
    color: #999;
    font-size: 14px;
  }
}
</style>
