<template>
  <view class="manifest">
    <view class="selector-overlay" v-if="showEventTypeDropdown" @click="hideEventTypeSelector">
      <view class="selector-content" @click.stop>
        <view class="selector-header">选择事件类型</view>
        <view
          class="selector-item"
          v-for="item in eventTypeOptions"
          :key="item.value"
          @click="selectEventType(item)"
        >
          <text>{{ item.label }}</text>
          <text v-if="selectedEventType.value === item.value" class="check">✓</text>
        </view>
      </view>
    </view>

    <view class="selector-overlay" v-if="showStatusDropdown" @click="hideStatusSelector">
      <view class="selector-content" @click.stop>
        <view class="selector-header">选择处置状态</view>
        <view
          class="selector-item"
          v-for="item in statusOptions"
          :key="item.value"
          @click="selectStatus(item)"
        >
          <text>{{ item.label }}</text>
          <text v-if="selectedStatus.value === item.value" class="check">✓</text>
        </view>
      </view>
    </view>

    <!-- 时间范围选择器 -->
    <view class="selector-overlay" v-if="showDateDropdown" @click="hideDateSelector">
      <view class="selector-content" @click.stop>
        <view class="selector-header">选择时间范围</view>
        <view
          class="selector-item"
          v-for="item in dateRangeOptions"
          :key="item.value"
          @click="selectDateRange(item)"
        >
          <text>{{ item.label }}</text>
          <text v-if="selectedDateRange.value === item.value" class="check">✓</text>
        </view>
      </view>
    </view>

    <!-- 事件列表 -->
    <view class="event-list">
      <view
        class="event-item"
        v-for="(item, index) in tableData"
        :key="index"
        @click="handleItemClick(item)"
      >
        <view class="event-content">
          <view class="event-header">
            <view class="event-tag" :class="getTagClass((item as any)?.eventType)">
              {{ (item as any)?.eventTypeName || '人员离岗' }}
            </view>
          </view>
          <view class="event-desc">
            {{ (item as any)?.description || '外运垃圾车辆空调器清洗一层中控室' }}
          </view>
          <view class="event-time">
            {{ (item as any)?.createTime || '2025-05-06 14:56:12' }}
          </view>
        </view>
        <view class="event-right">
          <image
            class="event-image"
            :src="(item as any)?.imageUrl || '/static/images/default-event.png'"
            mode="aspectFill"
          />
          <text class="arrow-right">›</text>
        </view>
      </view>
    </view>

    <!-- 底部提示 -->
    <view class="bottom-tip" v-if="tableData.length === 0">暂无相关事件</view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
  tableData: {
    type: Array,
    default: () => [],
  },
})

// 下拉框显示状态
const showEventTypeDropdown = ref(false)
const showStatusDropdown = ref(false)
const showDateDropdown = ref(false)

// 事件类型选项
const eventTypeOptions = ref([
  { label: '全部类型', value: 'all' },
  { label: '人员离岗', value: 'staff_leave' },
  { label: '疑似烟火', value: 'fire_smoke' },
  { label: '异常行为', value: 'abnormal_behavior' },
  { label: '设备异常', value: 'device_error' },
  { label: '环境异常', value: 'environment_error' },
])

// 处置状态选项
const statusOptions = ref([
  { label: '处置状态', value: 'all' },
  { label: '待处置', value: 'pending' },
  { label: '处置中', value: 'processing' },
  { label: '已处置', value: 'completed' },
  { label: '已忽略', value: 'ignored' },
])

// 时间范围选项
const dateRangeOptions = ref([
  { label: '2025-01-01 - 2025-09-01', value: 'default' },
  { label: '今天', value: 'today' },
  { label: '昨天', value: 'yesterday' },
  { label: '最近7天', value: 'week' },
  { label: '最近30天', value: 'month' },
  { label: '自定义时间', value: 'custom' },
])

// 当前选中的值
const selectedEventType = ref(eventTypeOptions.value[0])
const selectedStatus = ref(statusOptions.value[0])
const selectedDateRange = ref(dateRangeOptions.value[0])

// 显示下拉框方法
const showEventTypeSelector = () => {
  showEventTypeDropdown.value = true
}

const showStatusSelector = () => {
  showStatusDropdown.value = true
}

const showDateSelector = () => {
  showDateDropdown.value = true
}

// 隐藏下拉框方法
const hideEventTypeSelector = () => {
  showEventTypeDropdown.value = false
}

const hideStatusSelector = () => {
  showStatusDropdown.value = false
}

const hideDateSelector = () => {
  showDateDropdown.value = false
}

// 选择选项方法
const selectEventType = (item: any) => {
  selectedEventType.value = item
  hideEventTypeSelector()
  // 这里可以添加筛选逻辑
  console.log('选择事件类型:', item)
}

const selectStatus = (item: any) => {
  selectedStatus.value = item
  hideStatusSelector()
  // 这里可以添加筛选逻辑
  console.log('选择处置状态:', item)
}

const selectDateRange = (item: any) => {
  selectedDateRange.value = item
  hideDateSelector()
  // 这里可以添加筛选逻辑
  console.log('选择时间范围:', item)
}

// 获取事件类型标签样式
const getTagClass = (eventType: string) => {
  const typeMap: Record<string, string> = {
    人员离岗: 'tag-orange',
    疑似烟火: 'tag-green',
    异常行为: 'tag-red',
    设备异常: 'tag-blue',
  }
  return typeMap[eventType] || 'tag-orange'
}

// 处理事件项点击
const handleItemClick = (item: any) => {
  console.log('点击事件项:', item)
  // 这里可以添加跳转到事件详情页的逻辑
  uni.navigateTo({
    url: `/pages/eventDetail/eventDetail?id=${item.id}`,
  })
}
</script>

<style scoped lang="scss">
.manifest {
  background-color: #f7f7f7;
  min-height: 100vh;

  .filter-bar {
    display: flex;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: white;
    border-bottom: 1px solid #f0f0f0;

    .filter-item {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #666;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f5f5f5;
      }

      .arrow {
        margin-left: 4px;
        font-size: 12px;
        transition: transform 0.2s;
      }
    }
  }

  .selector-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;

    .selector-content {
      background-color: white;
      border-radius: 8px;
      width: 280px;
      max-height: 400px;
      overflow-y: auto;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);

      .selector-header {
        padding: 16px;
        font-size: 16px;
        font-weight: 500;
        color: #333;
        border-bottom: 1px solid #f0f0f0;
        text-align: center;
      }

      .selector-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        font-size: 14px;
        color: #333;
        border-bottom: 1px solid #f8f8f8;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background-color: #f5f5f5;
        }

        &:last-child {
          border-bottom: none;
        }

        .check {
          color: #007aff;
          font-weight: bold;
        }
      }
    }
  }

  .event-list {
    padding: 0 16px;

    .event-item {
      display: flex;
      align-items: center;
      padding: 16px 0;
      background-color: white;
      border-radius: 8px;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .event-content {
        flex: 1;
        padding: 0 16px;

        .event-header {
          margin-bottom: 8px;

          .event-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            color: white;

            &.tag-orange {
              background-color: #ff9500;
            }

            &.tag-green {
              background-color: #34c759;
            }

            &.tag-red {
              background-color: #ff3b30;
            }

            &.tag-blue {
              background-color: #007aff;
            }
          }
        }

        .event-desc {
          font-size: 14px;
          color: #333;
          line-height: 1.4;
          margin-bottom: 8px;
        }

        .event-time {
          font-size: 12px;
          color: #999;
        }
      }

      .event-right {
        display: flex;
        align-items: center;
        padding-right: 16px;

        .event-image {
          width: 60px;
          height: 40px;
          border-radius: 4px;
          margin-right: 8px;
        }

        .arrow-right {
          font-size: 18px;
          color: #ccc;
        }
      }
    }
  }

  .bottom-tip {
    text-align: center;
    padding: 40px 0;
    color: #999;
    font-size: 14px;
  }
}
</style>
