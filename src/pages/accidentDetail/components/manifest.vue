<template>
  <view class="manifest">
    <!-- 筛选条件 -->
    <wd-drop-menu>
      <wd-drop-menu-item
        title="全部类型"
        v-model="filterFrom.taskStatus"
        :options="eventTypeOptions"
        @change="emitSearch"
      />
      <wd-drop-menu-item
        title="处置状态"
        v-model="filterFrom.inspectionResult"
        :options="statusOptions"
        @change="emitSearch"
      />
      <view class="time-box" @click.stop="openPopup">
        <view>时间范围</view>
        <view>
          <img src="@/static/images/arrow.png" alt="" />
        </view>
      </view>
    </wd-drop-menu>

    <!-- 事件列表 -->
    <view class="event-list">
      <view
        class="event-item"
        v-for="(item, index) in tableData"
        :key="index"
        @click="handleItemClick(item)"
      >
        <view class="event-content">
          <view class="event-header">
            <view>{{ (item as any)?.eventTypeName || '222' }}</view>
            <view class="event-tag" :class="getTagClass((item as any)?.eventType)">
              {{ (item as any)?.eventTypeName || '111' }}
            </view>
          </view>
          <view class="event-desc">
            {{ (item as any)?.description || '44' }}
          </view>
          <view class="event-time">
            {{ (item as any)?.createTime || '2025-05-06 14:56:12' }}
          </view>
        </view>
        <view class="event-right">
          <image
            class="event-image"
            :src="(item as any)?.imageUrl || '/static/images/default-event.png'"
            mode="aspectFill"
          />
          <text class="arrow-right">›</text>
        </view>
      </view>
    </view>
  </view>
  <!-- 时间范围 -->
  <wd-calendar
    ref="calendar"
    :with-cell="false"
    type="daterange"
    v-model="timeValue"
    :max-range="360"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    :close-on-click-modal="false"
  ></wd-calendar>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import dayjs from 'dayjs'

const props = defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
  tableData: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['search'])

// 事件类型选项
const eventTypeOptions = ref([
  { label: '待开始', value: '0' },
  { label: '逾期', value: '1' },
  { label: '进行中', value: '2' },
  { label: '已完成', value: '3' },
])

// 处置状态选项
const statusOptions = ref([
  { label: '待处置', value: '1' },
  { label: '处置中', value: '2' },
  { label: '已处置', value: '3' },
  { label: '已忽略', value: '4' },
])

const timeValue = ref<number[]>([])
const calendar = ref<any>()

// 搜索条件对象
const filterFrom = ref({
  taskStatus: '',
  inspectionResult: '',
  planStartTime: '',
  planEndTime: '',
})

function handleConfirm({ value }) {
  filterFrom.value.planStartTime = dayjs(value[0]).startOf('days').format('YYYY-MM-DD HH:mm:ss')
  filterFrom.value.planEndTime = dayjs(value[1]).endOf('days').format('YYYY-MM-DD HH:mm:ss')
  emitSearch()
}

function handleCancel() {
  timeValue.value = []
  filterFrom.value.planStartTime = ''
  filterFrom.value.planEndTime = ''
  emitSearch()
}

function openPopup() {
  calendar.value?.open()
}

// 向父组件发送搜索条件
const emitSearch = () => {
  const searchParams = {
    taskStatus: filterFrom.value.taskStatus,
    inspectionResult: filterFrom.value.inspectionResult,
    startTime: filterFrom.value.planStartTime,
    endTime: filterFrom.value.planEndTime,
  }
  console.log('发送搜索条件:', searchParams)
  emit('search', searchParams)
}
// 获取事件类型标签样式
const getTagClass = (eventType: string) => {
  const typeMap: Record<string, string> = {
    人员离岗: 'tag-orange',
    疑似烟火: 'tag-green',
    异常行为: 'tag-red',
    设备异常: 'tag-blue',
  }
  return typeMap[eventType] || 'tag-orange'
}

// 处理事件项点击
const handleItemClick = (item: any) => {
  console.log('点击事件项:', item)
  uni.navigateTo({
    url: `/pages/eventDetail/eventDetail?id=${item.id}`,
  })
}
</script>

<style scoped lang="scss">
.manifest {
  height: 100vh;
  background-color: #f7f7f7;
  display: flex;
  flex-direction: column;

  // 确保下拉菜单样式正确
  :deep(.wd-drop-menu) {
    display: flex;
    align-items: center;
    width: 100%;
    padding-right: 34rpx;
    background-color: white;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.wd-drop-menu__list) {
    flex: 1;
  }

  // 时间选择器样式
  .time-box {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 240rpx;
    height: 44px;
    padding: 0 16rpx;
    font-size: 14px;
    color: #333;
    text-align: center;
    cursor: pointer;

    &:hover {
      background-color: #f5f5f5;
    }

    > view:first-child {
      flex: 1;
      text-align: center;
    }

    > view:last-child {
      position: absolute;
      right: 16rpx;
      bottom: 18rpx;
      scale: 0.7;
    }

    img {
      width: 12px;
      height: 12px;
      transition: transform 0.2s;
    }
  }

  .event-list {
    flex: 1;
    padding: 0 16px;

    .event-item {
      display: flex;
      align-items: center;
      padding: 16px 0;
      margin-bottom: 12px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .event-content {
        flex: 1;
        padding: 0 16px;

        .event-header {
          display: flex;
          margin-bottom: 20rpx;
          font-size: 28rpx;
          font-weight: 500;
          color: #212121;

          .event-tag {
            padding: 0 8rpx;
            margin-left: 24rpx;
            font-size: 24rpx;
            font-weight: 400;
            color: #ffffff;
            border-radius: 4px;

            &.tag-orange {
              background-color: #ff9500;
            }

            &.tag-green {
              background-color: #34c759;
            }

            &.tag-red {
              background-color: #ff3b30;
            }

            &.tag-blue {
              background-color: #007aff;
            }
          }
        }

        .event-desc {
          margin-bottom: 16rpx;
          font-size: 26rpx;
          font-weight: 400;
          line-height: 1.4;
          color: #808080;
        }

        .event-time {
          font-size: 26rpx;
          font-weight: 400;
          color: #999999;
        }
      }

      .event-right {
        display: flex;
        align-items: center;
        padding-right: 16px;

        .event-image {
          width: 60px;
          height: 40px;
          margin-right: 8px;
          border-radius: 4px;
        }

        .arrow-right {
          font-size: 18px;
          color: #ccc;
        }
      }
    }
  }

  .bottom-tip {
    padding: 40px 0;
    font-size: 14px;
    color: #999;
    text-align: center;
  }
}
</style>
