<template>
  <view class="manifest">
    <!-- 筛选条件 -->
    <!-- <view class="filter-bar">
      <wd-select
        v-model="selectedEventType"
        :options="eventTypeOptions"
        placeholder="全部类型"
        @change="onEventTypeChange"
        size="small"
      />
      <wd-select
        v-model="selectedStatus"
        :options="statusOptions"
        placeholder="处置状态"
        @change="onStatusChange"
        size="small"
      />
      <wd-select
        v-model="selectedDateRange"
        :options="dateRangeOptions"
        placeholder="时间范围"
        @change="onDateRangeChange"
        size="small"
      />
    </view> -->

    <!-- 事件列表 -->
    <view class="event-list">
      <view
        class="event-item"
        v-for="(item, index) in tableData"
        :key="index"
        @click="handleItemClick(item)"
      >
        <view class="event-content">
          <view class="event-header">
            <view class="event-tag" :class="getTagClass((item as any)?.eventType)">
              {{ (item as any)?.eventTypeName || '人员离岗' }}
            </view>
          </view>
          <view class="event-desc">
            {{ (item as any)?.description || '外运垃圾车辆空调器清洗一层中控室' }}
          </view>
          <view class="event-time">
            {{ (item as any)?.createTime || '2025-05-06 14:56:12' }}
          </view>
        </view>
        <view class="event-right">
          <image
            class="event-image"
            :src="(item as any)?.imageUrl || '/static/images/default-event.png'"
            mode="aspectFill"
          />
          <text class="arrow-right">›</text>
        </view>
      </view>
    </view>

    <!-- 底部提示 -->
    <view class="bottom-tip" v-if="tableData.length === 0">暂无相关事件</view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
  tableData: {
    type: Array,
    default: () => [],
  },
})

// 事件类型选项
const eventTypeOptions = ref([
  { label: '全部类型', value: 'all' },
  { label: '人员离岗', value: 'staff_leave' },
  { label: '疑似烟火', value: 'fire_smoke' },
  { label: '异常行为', value: 'abnormal_behavior' },
  { label: '设备异常', value: 'device_error' },
  { label: '环境异常', value: 'environment_error' },
])

// 处置状态选项
const statusOptions = ref([
  { label: '处置状态', value: 'all' },
  { label: '待处置', value: 'pending' },
  { label: '处置中', value: 'processing' },
  { label: '已处置', value: 'completed' },
  { label: '已忽略', value: 'ignored' },
])

// 时间范围选项
const dateRangeOptions = ref([
  { label: '2025-01-01 - 2025-09-01', value: 'default' },
  { label: '今天', value: 'today' },
  { label: '昨天', value: 'yesterday' },
  { label: '最近7天', value: 'week' },
  { label: '最近30天', value: 'month' },
  { label: '自定义时间', value: 'custom' },
])

// 当前选中的值
const selectedEventType = ref('all')
const selectedStatus = ref('all')
const selectedDateRange = ref('default')

// wd-select 变化处理方法
const onEventTypeChange = (value: string) => {
  console.log('选择事件类型:', value)
  // 这里可以添加筛选逻辑
}

const onStatusChange = (value: string) => {
  console.log('选择处置状态:', value)
  // 这里可以添加筛选逻辑
}

const onDateRangeChange = (value: string) => {
  console.log('选择时间范围:', value)
  // 这里可以添加筛选逻辑
}

// 获取事件类型标签样式
const getTagClass = (eventType: string) => {
  const typeMap: Record<string, string> = {
    人员离岗: 'tag-orange',
    疑似烟火: 'tag-green',
    异常行为: 'tag-red',
    设备异常: 'tag-blue',
  }
  return typeMap[eventType] || 'tag-orange'
}

// 处理事件项点击
const handleItemClick = (item: any) => {
  console.log('点击事件项:', item)
  // 这里可以添加跳转到事件详情页的逻辑
  uni.navigateTo({
    url: `/pages/eventDetail/eventDetail?id=${item.id}`,
  })
}
</script>

<style scoped lang="scss">
.manifest {
  background-color: #f7f7f7;
  min-height: 100vh;

  .filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: white;
    border-bottom: 1px solid #f0f0f0;
    gap: 12px;

    :deep(.wd-select) {
      flex: 1;
      min-width: 0;

      .wd-select__inner {
        font-size: 14px;
        height: 32px;
        line-height: 32px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        background-color: #fff;
      }

      .wd-select__placeholder {
        color: #999;
        font-size: 14px;
      }

      .wd-select__value {
        color: #333;
        font-size: 14px;
      }
    }
  }

  .event-list {
    padding: 0 16px;

    .event-item {
      display: flex;
      align-items: center;
      padding: 16px 0;
      background-color: white;
      border-radius: 8px;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .event-content {
        flex: 1;
        padding: 0 16px;

        .event-header {
          margin-bottom: 8px;

          .event-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            color: white;

            &.tag-orange {
              background-color: #ff9500;
            }

            &.tag-green {
              background-color: #34c759;
            }

            &.tag-red {
              background-color: #ff3b30;
            }

            &.tag-blue {
              background-color: #007aff;
            }
          }
        }

        .event-desc {
          font-size: 14px;
          color: #333;
          line-height: 1.4;
          margin-bottom: 8px;
        }

        .event-time {
          font-size: 12px;
          color: #999;
        }
      }

      .event-right {
        display: flex;
        align-items: center;
        padding-right: 16px;

        .event-image {
          width: 60px;
          height: 40px;
          border-radius: 4px;
          margin-right: 8px;
        }

        .arrow-right {
          font-size: 18px;
          color: #ccc;
        }
      }
    }
  }

  .bottom-tip {
    text-align: center;
    padding: 40px 0;
    color: #999;
    font-size: 14px;
  }
}
</style>
