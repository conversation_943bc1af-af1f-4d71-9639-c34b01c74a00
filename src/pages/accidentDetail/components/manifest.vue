<template>
  <view class="manifest">
    <!-- 筛选条件 -->
    <wd-drop-menu style="z-index: 99">
      <wd-drop-menu-item
        title="全部类型"
        v-model="selectedEventType"
        :options="eventTypeOptions"
        @change="onEventTypeChange"
      />
      <wd-drop-menu-item
        title="处置状态"
        v-model="selectedStatus"
        :options="statusOptions"
        @change="onStatusChange"
      />
      <view class="time-box" @click.stop="openPopup(0)">
        <view>作业起止时间</view>
        <view>
          <img src="@/static/icon/arrow.png" alt="" />
        </view>
      </view>
    </wd-drop-menu>

    <!-- 作业起止时间 -->
    <wd-calendar
      ref="calendar"
      :key="Math.random()"
      :with-cell="false"
      type="daterange"
      v-model="timeValue"
      :max-range="360"
      @confirm="handleConfirm"
      @cancel="handleCancel"
      :close-on-click-modal="false"
    ></wd-calendar>

    <!-- 事件列表 -->
    <view class="event-list">
      <view
        class="event-item"
        v-for="(item, index) in tableData"
        :key="index"
        @click="handleItemClick(item)"
      >
        <view class="event-content">
          <view class="event-header">
            <view class="event-tag" :class="getTagClass((item as any)?.eventType)">
              {{ (item as any)?.eventTypeName || '人员离岗' }}
            </view>
          </view>
          <view class="event-desc">
            {{ (item as any)?.description || '外运垃圾车辆空调器清洗一层中控室' }}
          </view>
          <view class="event-time">
            {{ (item as any)?.createTime || '2025-05-06 14:56:12' }}
          </view>
        </view>
        <view class="event-right">
          <image
            class="event-image"
            :src="(item as any)?.imageUrl || '/static/images/default-event.png'"
            mode="aspectFill"
          />
          <text class="arrow-right">›</text>
        </view>
      </view>
    </view>

    <!-- 底部提示 -->
    <view class="bottom-tip" v-if="tableData.length === 0">暂无事件清单</view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import dayjs from 'dayjs'

defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
  tableData: {
    type: Array,
    default: () => [],
  },
})

// 事件类型选项
const eventTypeOptions = ref([
  { label: '全部', value: 'all' },
  { label: '人员离岗', value: 'staff_leave' },
  { label: '疑似烟火', value: 'fire_smoke' },
  { label: '异常行为', value: 'abnormal_behavior' },
  { label: '设备异常', value: 'device_error' },
  { label: '环境异常', value: 'environment_error' },
])

// 处置状态选项
const statusOptions = ref([
  { label: '全部', value: 'all' },
  { label: '待处置', value: 'pending' },
  { label: '处置中', value: 'processing' },
  { label: '已处置', value: 'completed' },
  { label: '已忽略', value: 'ignored' },
])

const selectedEventType = ref('all')
const selectedStatus = ref('all')
const timeValue = ref<number[]>([])

const onEventTypeChange = (event: any) => {
  const value = event.selectedItem.value
  console.log('选择事件类型:', value)
}

const onStatusChange = (event: any) => {
  const value = event.selectedItem.value
  console.log('选择处置状态:', value)
}

function handleConfirm({ value }) {
  filterFrom.value.planStartTime = dayjs(value[0]).startOf('days').format('YYYY-MM-DD HH:mm:ss')
  filterFrom.value.planEndTime = dayjs(value[1]).endOf('days').format('YYYY-MM-DD HH:mm:ss')
}

function handleCancel() {
  timeValue.value = []
  filterFrom.value.planStartTime = ''
  filterFrom.value.planEndTime = ''
}

function openPopup() {
  calendar.value?.open()
}
// 获取事件类型标签样式
const getTagClass = (eventType: string) => {
  const typeMap: Record<string, string> = {
    人员离岗: 'tag-orange',
    疑似烟火: 'tag-green',
    异常行为: 'tag-red',
    设备异常: 'tag-blue',
  }
  return typeMap[eventType] || 'tag-orange'
}

// 处理事件项点击
const handleItemClick = (item: any) => {
  console.log('点击事件项:', item)
  // 这里可以添加跳转到事件详情页的逻辑
  uni.navigateTo({
    url: `/pages/eventDetail/eventDetail?id=${item.id}`,
  })
}
</script>

<style scoped lang="scss">
.manifest {
  background-color: #f7f7f7;
  min-height: 100vh;

  // 确保下拉菜单样式正确
  :deep(.wd-drop-menu) {
    z-index: 99;
    background-color: white;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.wd-drop-menu-item) {
    z-index: 100;

    .wd-drop-menu-item__title {
      font-size: 14px;
      color: #333;
    }

    .wd-drop-menu-item__content {
      background-color: white;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .wd-drop-menu-item__option {
      font-size: 14px;
      color: #333;
      padding: 8px 16px;

      &:hover {
        background-color: #f5f5f5;
      }

      &.is-active {
        color: #007aff;
        background-color: #f0f8ff;
      }
    }
  }

  // 日历选择器样式
  :deep(.wd-calendar) {
    .wd-calendar__header {
      background-color: #007aff;
      color: white;
    }

    .wd-calendar__title {
      color: white;
      font-weight: 500;
    }

    .wd-calendar__body {
      background-color: white;
    }

    .wd-calendar__day {
      &.is-selected {
        background-color: #007aff;
        color: white;
      }

      &.is-range {
        background-color: rgba(0, 122, 255, 0.1);
      }

      &.is-range-start,
      &.is-range-end {
        background-color: #007aff;
        color: white;
      }
    }

    .wd-calendar__footer {
      padding: 16px;
      border-top: 1px solid #f0f0f0;

      .wd-button {
        margin: 0 8px;
      }
    }
  }

  .event-list {
    padding: 0 16px;

    .event-item {
      display: flex;
      align-items: center;
      padding: 16px 0;
      background-color: white;
      border-radius: 8px;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .event-content {
        flex: 1;
        padding: 0 16px;

        .event-header {
          margin-bottom: 8px;

          .event-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            color: white;

            &.tag-orange {
              background-color: #ff9500;
            }

            &.tag-green {
              background-color: #34c759;
            }

            &.tag-red {
              background-color: #ff3b30;
            }

            &.tag-blue {
              background-color: #007aff;
            }
          }
        }

        .event-desc {
          font-size: 14px;
          color: #333;
          line-height: 1.4;
          margin-bottom: 8px;
        }

        .event-time {
          font-size: 12px;
          color: #999;
        }
      }

      .event-right {
        display: flex;
        align-items: center;
        padding-right: 16px;

        .event-image {
          width: 60px;
          height: 40px;
          border-radius: 4px;
          margin-right: 8px;
        }

        .arrow-right {
          font-size: 18px;
          color: #ccc;
        }
      }
    }
  }

  .bottom-tip {
    text-align: center;
    padding: 40px 0;
    color: #999;
    font-size: 14px;
  }
}
</style>
