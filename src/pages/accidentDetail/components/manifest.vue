<template>
  <view class="manifest">
    <view class="info">
      <view>
        <text>所属单位：</text>
        {{ info.deptName || '--' }}
      </view>
      <view>
        <text>任务时间：</text>
        {{ info.taskPlanStartTime }} ~ {{ info.taskPlanEndTime }}
      </view>
      <view>
        <text>任务状态：</text>
        {{ info.taskStatusName }}
      </view>
      <view>
        <text>事件总数：</text>
        {{ tableData.length }}个
      </view>
    </view>
    <view class="table">
      <ManifestItem v-for="(item, index) in tableData" :key="index" :data="item" />
    </view>
  </view>
</template>

<script setup lang="ts">
import ManifestItem from './accidentList.vue'

defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
  tableData: {
    type: Array,
    default: () => [],
  },
})
</script>

<style scoped lang="scss">
.manifest {
  font-size: 14px;
  .info {
    padding: 10px;
    background-color: white;
    view {
      margin-bottom: 12px;
      text {
        color: #7f7f7f;
      }
    }
  }
  .table {
    width: 345px;
    margin: 0 auto;
    margin-bottom: 50px;
    margin-top: 26px;
  }
}
</style>
