import { http } from '@/utils/http'

const SERVER: string = import.meta.env.VITE_SERVER_PORT
// 事故详情
export const getAccidentDetail = (param: any) => {
  return http.get<any>(`${SERVER}/video/task/taskTopDetail`, { ...param })
}

// 浏览量新增
export const addViewDetail = (data: any) => {
  return http.post<any>(`${SERVER}/regulation/addViewDetail`, { ...data })
}

export const taskInspectionDetailStatisticAPI = (param: any) => {
  return http.post<any>(`${SERVER}/video/task/taskInspectionDetailStatistic`, param)
}

// 巡检清单
export const taskInspectionDetailAPI = (param: any) => {
  return http.post<any>(`${SERVER}/video/task/taskInspectionPage`, param)
}
