<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '事故详情',
    navigationBarBackgroundColor: '#0087fc',
    navigationBarTextStyle: 'white',
  },
}
</route>
<template>
  <z-paging ref="paging" v-model="tableData" @query="getList">
    <view class="accident-detail">
      <CustomTabs :tabs="tabs" :activeIndex="activeIndex" @handleClick="handleChange"></CustomTabs>
      <Detail :tableData="tableData" :info="accidentInfo" v-if="activeIndex == 0" />
      <Manifest
        :tableData="manifestData"
        :info="accidentInfo"
        @search="handleManifestSearch"
        v-else
      />
    </view>
  </z-paging>

  <button
    style="
      position: fixed;
      bottom: 0;
      z-index: 10000;
      width: 100vw;
      color: white;
      background-color: #0256ff;
      border-radius: 0;
    "
  >
    巡检路径
  </button>
</template>

<script lang="ts" setup>
import { getAccidentDetail, taskInspectionDetailAPI } from './fetch'
import { queryVideoTaskDisposeByVideoDeviceIdAPI } from '@/pages/index/fetch'
import CustomTabs from './components/custom-Tabs.vue'
import Detail from './components/detail.vue'
import Manifest from './components/manifest.vue'

const tabs = ref(['任务详情', '事件清单'])

const activeIndex = ref(0)
function handleChange(event: number) {
  activeIndex.value = event
  // 切换tab时重新加载数据
  paging.value?.reload()
}

const paging = ref()
const tableData = ref([
  {
    id: 1,
    num: '01',
    title: '场站厂站平面围墙南1',
    taskStatus: 0,
    taskStatusName: '待开始',
    imgSrc: '',
    yichang: '',
    time: '2025-05-20 14：25：32',
  },
  {
    id: 1,
    num: '01',
    title: '场站厂站平面围墙南1',
    taskStatus: 1,
    taskStatusName: '异常',
    yichang: '异常异常',
    imgSrc: '',
    time: '2025-05-20 14：25：32',
  },
])

// 事件清单数据
const manifestData = ref([])
const param = ref({
  pageNo: 1,
  pageSize: 10,
  deviceId: '20240920215325244924',
  zhId: 'ycsyqt',
  createdBy: '87d73a3a59d34379bfcd56eb50ecfd96',
})

// 事件清单搜索参数
const manifestSearchParams = ref({
  taskStatus: '',
  inspectionResult: '',
  startTime: '',
  endTime: '',
})

const getList = async (pageNo: number) => {
  try {
    uni.showLoading({ title: '加载中...', mask: true })
    param.value.pageNo = pageNo

    if (activeIndex.value === 0) {
      // 任务详情tab - 调用原有接口
      const res = await queryVideoTaskDisposeByVideoDeviceIdAPI({
        ...param.value,
        taskId: tId.value,
      })
      if (res && res.data) {
        paging.value.complete(res.data.rows || [])
      } else {
        paging.value.complete([])
      }
    } else {
      // 事件清单tab - 调用事件清单接口，包含搜索参数
      const searchParams = {
        ...param.value,
        taskId: tId.value,
        taskStatus: manifestSearchParams.value.taskStatus,
        inspectionResult: manifestSearchParams.value.inspectionResult,
        startTime: manifestSearchParams.value.startTime || '',
        endTime: manifestSearchParams.value.endTime || '',
      }

      const res = await taskInspectionDetailAPI(searchParams)
      if (res && res.data) {
        const manifestList = res.data.rows || []
        manifestData.value = manifestList
        paging.value.complete(manifestList)
      } else {
        manifestData.value = []
        paging.value.complete([])
      }
    }

    uni.hideLoading()
  } catch (error) {
    console.error('获取列表失败:', error)
    paging.value.complete(false)
    uni.hideLoading()
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  }
}

// 处理事件清单搜索
const handleManifestSearch = (searchParams: any) => {
  manifestSearchParams.value = {
    taskStatus: searchParams.taskStatus,
    inspectionResult: searchParams.inspectionResult,
    startTime: searchParams.startTime,
    endTime: searchParams.endTime,
  }
  // 重新加载数据
  if (activeIndex.value === 1) {
    paging.value?.reload()
  }
}

const accidentInfo = ref<any>({})
const getDetailData = async (id: string) => {
  try {
    const res = await getAccidentDetail({ taskId: id })
    accidentInfo.value = res.data
  } catch (error) {}
}

const tId = ref('')
onLoad((o) => {
  tId.value = o.id
  getDetailData(o.id)
  paging.value?.reload()
})
</script>

<style lang="scss" scoped>
.accident-detail {
  overflow: auto;
  background-color: #f7f7f7;
}
</style>
