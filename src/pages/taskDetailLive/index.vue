<route lang="json5" type="page">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '智能巡检详情',
  },
}
</route>
<template>
  <z-paging ref="paging" v-model="historyList" @query="gethistoryList" :refresher-enabled="false">
    <template #top>
      <SafetyNavbar :title="title"></SafetyNavbar>
      <view class="video">视频</view>
    </template>
    <view class="container">
      <view class="infoview">
        <view class="text-[#212121FF] text-[16px] font-[500]">基本信息</view>
        <view v-for="(itme, index) in infoParam" :key="index">
          <view v-if="itme.key == 'status'" class="info-item">
            {{ itme.lable }}
            <view v-if="info?.erecordDeviceInfo?.[itme.key] == '1'" class="text-[#24A143FF]">
              在线
            </view>
            <view v-if="info?.erecordDeviceInfo?.[itme.key] == '0'" class="text-[#232323FF]">
              离线
            </view>
          </view>
          <view v-else class="info-item">
            {{ itme.lable }}
            {{ info?.erecordDeviceInfo?.[itme.key] || '' }}
            <view v-if="info?.erecordDeviceInfo?.[itme.key] == '1'" class="">在线</view>

            <view v-if="info?.erecordDeviceInfo?.[itme.key] == '0'">离线</view>
            <wd-button v-if="itme.key == 'buildingName'" size="small" plain>查看位置</wd-button>
          </view>
        </view>
      </view>
      <view class="infoview">
        <view class="text-[#212121FF] text-[16px] font-[500]">历史事件</view>
        <view
          class="flex justify-between mt-[16px] pb-[16px]"
          style="border-bottom: 1px solid #e9ebf0ff"
          v-for="($item, index) in historyList"
          :key="index"
        >
          <view>
            <view class="flex items-center">
              <view class="text-[#212121FF] text-[14px] font-[500]">{{ $item.eventTypeName }}</view>

              <view
                class="w-[44px] h-[19px] text-[#FFFFFFFF] text-[12px] font-[400] rounded-[4px] text-center ml-[10px]"
                :style="{ background: disposeStatusObj[+$item.disposeStatus - 1].color }"
              >
                {{ disposeStatusObj[+$item.disposeStatus - 1].lable }}
              </view>
            </view>
            <view class="text-[#999999FF] text-[13px] font-[400] mt-[10px]">
              {{ $item.deviceTime }}
            </view>
          </view>
          <view>
            <image class="w-[78px] h-[44px]" :src="$item.videoUrl" />
            <!-- src="https://img10.360buyimg.com/jmadvertisement/jfs/t1/70325/36/14954/36690/5dcd3e3bEee5006e0/aed1ccf6d5ffc764.png" -->
            <wd-icon name="arrow-right" color="#666666FF" size="22px"></wd-icon>
          </view>
        </view>
      </view>
    </view>
  </z-paging>
</template>
<script lang="ts" setup>
import SafetyNavbar from '@/components/safety-navbar.vue'
import { getVideoDeviceInfoAPI, queryVideoTaskDisposeByVideoDeviceIdAPI } from '../index/fetch'
const props = defineProps({
  title: {
    type: String,
    default: '详情',
  },
})
// 1：解决，2：未解决
const disposeStatusObj: any = [
  { lable: '已处置', color: '#67C23A' },
  { lable: '未处置', color: '#F56C6C' },
]
const info = ref<any>({})
const historyList = ref<any[]>([])

const param = ref({
  pageNo: 1,
  pageSize: 10,
  deviceId: '20240920215325244924',
  zhId: 'ycsyqt',
  createdBy: '87d73a3a59d34379bfcd56eb50ecfd96',
})
const infoParam = ref<any[]>([
  { lable: '单位：', key: 'unitName' },
  { lable: '位置：', key: 'buildingName' },
  { lable: '品牌型号：', key: 'brand' },
  { lable: '设备编号：', key: 'deviceId' },
  { lable: '状态：', key: 'status' },
])

function getInfo() {
  getVideoDeviceInfoAPI({
    deviceId: '20221027172751755226',
    zhId: 'ycsyqt',
    createdBy: '87d73a3a59d34379bfcd56eb50ecfd96',
  }).then((res: any) => {
    console.log(res)
    info.value = res.data
  })
}
onMounted(() => {
  getInfo()
})
const paging = ref<any>()

async function gethistoryList(pageNo: number) {
  try {
    uni.showLoading({ title: '加载中...', mask: true })
    param.value.pageNo = pageNo
    const res = await queryVideoTaskDisposeByVideoDeviceIdAPI(param.value)
    console.log('历史数据 res = ', res)
    if (res && res.data) {
      paging.value.complete(res.data.rows || [])
    } else {
      paging.value.complete([])
    }

    uni.hideLoading()
  } catch (error) {
    console.error('获取列表失败:', error)
    paging.value.complete(false)
    uni.hideLoading()
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  }
}
</script>
<style lang="scss" scoped>
.video {
  width: 99%;
  height: 25vh;
  margin-top: 45px;
  border: 1px solid red;
}
.infoview {
  width: 85%;
  padding: 15px;
  margin: auto;
  margin-top: 11px;
  background-color: white;
  border-radius: 6px 6px 6px 6px;
  .info-item {
    @apply flex text-[#212121FF] text-[14px] font-[400] mt-[6px];
  }
}
</style>
